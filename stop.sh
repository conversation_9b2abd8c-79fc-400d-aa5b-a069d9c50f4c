#!/bin/bash

# ==============================================================================
# SEMS Agents Projesi - Durdurma Betiği
# ==============================================================================
# Bu betik, projenin tüm Docker servislerini durdurur ve kaynakları temizler.
#
# KULLANIM: ./stop.sh
# ==============================================================================

# Renkli çıktı için fonksiyonlar
print_info() {
    echo -e "\033[1;34m[BİLGİ]\033[0m $1"
}
print_success() {
    echo -e "\033[1;32m[BAŞARILI]\033[0m $1"
}

print_info "--- Tüm servisler durduruluyor ve kaynaklar temizleniyor... ---"

# -v bayrağı, oluşturulan isimsiz volümleri de siler.
# --remove-orphans, artık compose dosyasında olmayan servisleri temizler.
docker compose down -v --remove-orphans

print_success "Tüm servisler başarıyla durduruldu."
