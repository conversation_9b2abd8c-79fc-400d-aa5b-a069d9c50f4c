# SEMS Agents Proje<PERSON>je, Docker Compose ile yönetilen birden çok hizmeti içeren bir monorepo'dur. <PERSON><PERSON> kendine barı<PERSON>ırılan (self-hosted) bir Supabase örneği ve özel uygulama hizmet<PERSON>ini (`frontend`, `backend`, `agent-core`) içerir.

## Proje <PERSON>

- `apps/`: Frontend, backend ve agent-core uygulamalarının kaynak kodlarını içerir.
- `docker/`: Supabase gibi üçüncü parti hizmetler için Docker yapılandırmalarını içerir.
- `docker-compose.yml`: <PERSON>zel uygulamalar için ana Docker Compose dosyası.
- `start-dev.sh`: Geliştirme ortamını otomatik olarak başlatan betik.

## Geliştirme Ortamı Kurulumu

Tüm geliştirme ortamını tek bir komutla başlatabilirsiniz.

### Gereksinimler

-   <PERSON>er ve Docker Compose
-   `bash` uyumlu bir terminal (Windows için Git Bash veya WSL önerilir).

### Başlatma

1.  Projenin ana dizininde bir terminal açın.
2.  Aşağıdaki komutu çalıştırın:

    ```bash
    ./start-dev.sh
    ```

Bu betik, Supabase ve diğer tüm uygulama servislerini otomatik olarak yapılandıracak, derleyecek ve başlatacaktır. İlk çalıştırma, Docker imajlarının indirilmesi nedeniyle biraz zaman alabilir.

### Erişilebilir Servisler

Kurulum tamamlandığında, aşağıdaki adreslerden servislere erişebilirsiniz:

-   **Frontend:** `http://localhost:3000`
-   **Backend API:** `http://localhost:8001`
-   **Supabase Studio:** `http://localhost:8082`
-   **Geliştirme E-posta Sunucusu (Inbucket):** `http://localhost:9000`
-   **Flowise UI:** `http://localhost:3001`

### Ortamı Durdurma

Geliştirme ortamındaki tüm servisleri (verileri silmeden) durdurmak için:

```bash
./stop-dev.sh
```

Tüm sistemi (Supabase dahil) durdurmak ve temizlemek için `docker/supabase` dizinindeki `reset.sh` betiğini kullanabilirsiniz.