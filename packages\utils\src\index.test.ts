import { expect, test } from 'vitest';
import { cn } from './index';

test('cn should merge tailwind classes correctly', () => {
  expect(cn('bg-red-500', 'text-white')).toBe('bg-red-500 text-white');
});

test('cn should handle conditional classes', () => {
  expect(cn('p-4', { 'font-bold': true, 'italic': false })).toBe('p-4 font-bold');
});

test('cn should override conflicting classes', () => {
  expect(cn('p-2', 'p-4')).toBe('p-4');
});
