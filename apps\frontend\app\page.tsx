import Image from "next/image";

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="z-10 w-full max-w-5xl items-center justify-between font-mono text-sm lg:flex">
        <p className="fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto  lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30">
          SemsAgents Projesi <PERSON>ılıyor...&nbsp;
          <code className="font-mono font-bold">apps/frontend/app/page.tsx</code>
        </p>
      </div>

      <div className="relative z-[-1] flex place-items-center text-6xl font-bold">
        <h1><PERSON><PERSON> Geldiniz!</h1>
      </div>

      <div className="mb-32 mt-16 grid text-center lg:mb-0 lg:w-full lg:max-w-5xl lg:grid-cols-3 lg:text-left">
        <a
          href="#"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Agent'lar
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Kendi özel AI agent takımlarınızı oluşturun ve yönetin.
          </p>
        </a>

        <a
          href="#"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Entegrasyonlar
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Notion, Slack, Google Calendar gibi araçlarla kolayca entegre olun.
          </p>
        </a>

        <a
          href="#"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Multi-Tenant
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Her tenant için tamamen izole ve güvenli bir altyapı.
          </p>
        </a>
      </div>
    </main>
  );
}
