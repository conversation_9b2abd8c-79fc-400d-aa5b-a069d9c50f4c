#!/bin/bash

# ==============================================================================
# SEMS Agents Projesi - Docker İmajlarını Önceden Çekme Betiği
# ==============================================================================
# Bu betik, docker-compose dosyalarında tanımlı olan tüm imajları
# 'docker compose up' komutundan önce sırayla çeker. Bu, özellikle yavaş
# internet bağlantılarında veya paralel indirme sırasında oluşabilecek
# ağ hatalarını ve zaman aşımlarını önlemeye yardımcı olur.
#
# 'start-dev.sh' betiği tarafından otomatik olarak çağrılır.
# ==============================================================================

# Renkli çıktı için fonksiyonlar
print_info() {
    echo -e "\033[1;34m[BİLGİ]\033[0m $1"
}
print_success() {
    echo -e "\033[1;32m[BAŞARILI]\033[0m $1"
}
print_error() {
    echo -e "\033[1;31m[HATA]\033[0m $1"
}

# Betiğin herhangi bir komutta hata alması durumunda durmasını sağla
set -e

print_info "Proje için gerekli Docker imajları kontrol ediliyor ve çekiliyor..."

# Projede kullanılan tüm docker-compose dosyalarının listesi
COMPOSE_FILES=(
  "docker-compose.yml"
  "docker/flowise/docker-compose.yml"
  "docker/supabase/docker-compose.yml"
  "docker/supabase/dev/docker-compose.dev.yml"
)

# Sadece var olan dosyaları işle
VALID_COMPOSE_FILES=()
for file in "${COMPOSE_FILES[@]}"; do
    if [ -f "$file" ]; then
        VALID_COMPOSE_FILES+=("$file")
    fi
done

# Compose dosyalarından tüm 'image:' satırlarını bul, temizle ve eşsiz listesini oluştur.
IMAGES=$(grep -h 'image:' "${VALID_COMPOSE_FILES[@]}" | \
         sed -e 's/#.*//' -e 's/^[ \t]*//' -e 's/[ \t]*$//' | \
         awk '{print $2}' | \
         grep -v -E '^\$|\{|\}' | \
         sort -u || true)

if [ -z "$IMAGES" ]; then
    print_success "Çekilecek yeni bir public imaj bulunamadı. Muhtemelen hepsi yerelde mevcut."
    exit 0
fi

print_info "Aşağıdaki Docker imajları sırayla çekilecek:"
echo "$IMAGES"
echo

# Her bir imajı sırayla çek
for IMAGE in $IMAGES; do
    print_info "Imaj çekiliyor: $IMAGE"
    docker pull "$IMAGE"
done

echo
print_success "Tüm gerekli Docker imajları başarıyla çekildi/doğrulandı."
