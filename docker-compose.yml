# ==============================================================================
# SEMS Agents Projesi - Birleştirilmiş Docker Compose Yapılandırması
# ==============================================================================
# Bu dosya, projenin tüm servislerini (Supabase, Flowise, Uygulama)
# tek bir yerden yönetir.
#
# KULLANIM:
# Başlat: docker compose up -d --build
# Durdur:  docker compose down
# ==============================================================================

version: '3.8'

networks:
  sems_network:
    name: sems_network

volumes:
  db_data:
    name: sems_agents_db_data
  storage_data:
    name: sems_agents_storage_data
  flowise_data:
    name: sems_agents_flowise_data
  db_config:
    name: sems_agents_db_config

services:
  # ----------------------------------------------------------------------------
  # UYGULAMA SERVİSLERİ
  # ----------------------------------------------------------------------------
  frontend:
    container_name: sems-frontend
    build:
      context: ./apps/frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./apps/frontend:/app
      - /app/node_modules
      - /app/.next
    env_file:
      - .env
    networks:
      - sems_network
    depends_on:
      - backend
      - supabase-kong
    restart: unless-stopped

  backend:
    container_name: sems-backend
    build:
      context: ./apps/backend
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    volumes:
      - ./apps/backend:/app
      - /app/node_modules
    env_file:
      - .env
    networks:
      - sems_network
    restart: unless-stopped

  agent-core:
    container_name: sems-agent-core
    build:
      context: ./apps/agent-core
      dockerfile: Dockerfile
    volumes:
      - ./apps/agent-core:/app
      - /app/node_modules
    env_file:
      - .env
    networks:
      - sems_network
    restart: unless-stopped

  # ----------------------------------------------------------------------------
  # FLOWISE SERVİSİ
  # ----------------------------------------------------------------------------
  flowise:
    image: flowiseai/flowise:3.0.4
    container_name: sems-flowise
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - flowise_data:/root/.flowise
    env_file:
      - .env # Flowise .env değişkenlerini de ana .env'den alabilir
    networks:
      - sems_network

  # ----------------------------------------------------------------------------
  # SUPABASE SERVİSLERİ
  # ----------------------------------------------------------------------------
  supabase-studio:
    container_name: supabase-studio
    image: supabase/studio:2025.07.07-sha-1d3b0ba
    restart: unless-stopped
    ports:
      - "8082:3000"
    healthcheck:
      test: ["CMD", "node", "-e", "fetch('http://localhost:3000/api/platform/profile').then(r => {if(r.status !== 200) throw new Error(r.status)})"]
      timeout: 10s
      interval: 5s
      retries: 3
    depends_on:
      supabase-analytics:
        condition: service_healthy
    environment:
      STUDIO_PG_META_URL: http://supabase-meta:8080
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      DEFAULT_ORGANIZATION_NAME: ${DEFAULT_ORGANIZATION_NAME}
      DEFAULT_PROJECT_NAME: ${DEFAULT_PROJECT_NAME}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_PUBLIC_URL: http://localhost:8000
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
      AUTH_JWT_SECRET: ${JWT_SECRET}
      LOGFLARE_PRIVATE_ACCESS_TOKEN: ${LOGFLARE_API_KEY}
      LOGFLARE_URL: http://supabase-analytics:4000
      NEXT_PUBLIC_ENABLE_LOGS: "true"
      NEXT_ANALYTICS_BACKEND_PROVIDER: postgres
    networks:
      - sems_network

  supabase-kong:
    container_name: supabase-kong
    image: kong:3.7
    restart: unless-stopped
    ports:
      - "8000:8000"
      - "8443:8443"
    volumes:
      - ./config/api/kong.yml:/home/<USER>/temp.yml:ro
    depends_on:
      supabase-analytics:
        condition: service_healthy
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /home/<USER>/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
    entrypoint: bash -c 'eval "echo \"$$(cat ~/temp.yml)\"" > ~/kong.yml && /docker-entrypoint.sh kong docker-start'
    networks:
      - sems_network

  supabase-auth:
    container_name: supabase-auth
    image: supabase/gotrue:v2.177.0
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9999/health" ]
      timeout: 5s
      interval: 5s
      retries: 3
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: http://localhost:8000
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://supabase_auth_admin:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: "true"
      GOTRUE_MAILER_AUTOCONFIRM: "true"
      GOTRUE_SMTP_ADMIN_EMAIL: no-reply@localhost
      GOTRUE_SMTP_HOST: supabase-mail
      GOTRUE_SMTP_PORT: 2500
      GOTRUE_SMTP_USER: ""
      GOTRUE_SMTP_PASS: ""
    networks:
      - sems_network

  supabase-rest:
    container_name: supabase-rest
    image: postgrest/postgrest:v13.0.4
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    environment:
      PGRST_DB_URI: postgres://authenticator:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
    networks:
      - sems_network

  supabase-realtime:
    container_name: supabase-realtime
    image: supabase/realtime:v2.35.1
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    healthcheck:
      test: [ "CMD", "curl", "-sSfL", "--head", "-o", "/dev/null", "http://localhost:4000/api/health" ]
      timeout: 5s
      interval: 5s
      retries: 3
    environment:
      PORT: 4000
      DB_HOST: supabase-db
      DB_PORT: 5432
      DB_USER: supabase_admin
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_NAME: postgres
      API_JWT_SECRET: ${JWT_SECRET}
      SECRET_KEY_BASE: ${REALTIME_SECRET_KEY_BASE}
    networks:
      - sems_network

  supabase-storage:
    container_name: supabase-storage
    image: supabase/storage-api:v1.25.1
    restart: unless-stopped
    volumes:
      - storage_data:/var/lib/storage
    healthcheck:
      test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/status" ]
      timeout: 5s
      interval: 5s
      retries: 3
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_started
    environment:
      ANON_KEY: ${ANON_KEY}
      SERVICE_KEY: ${SERVICE_ROLE_KEY}
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgres://supabase_storage_admin:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
    networks:
      - sems_network

  supabase-imgproxy:
    container_name: supabase-imgproxy
    image: darthsim/imgproxy:v3.22.0
    restart: unless-stopped
    volumes:
      - storage_data:/var/lib/storage
    healthcheck:
      test: [ "CMD", "imgproxy", "health" ]
      timeout: 5s
      interval: 5s
      retries: 3
    environment:
      IMGPROXY_BIND: ":5001"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
    networks:
      - sems_network

  supabase-meta:
    container_name: supabase-meta
    image: supabase/postgres-meta:v0.90.0
    restart: unless-stopped
    ports:
      - "5555:8080"
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-analytics:
        condition: service_healthy
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: supabase-db
      PG_META_DB_PASSWORD: ${POSTGRES_PASSWORD}
    networks:
      - sems_network

  supabase-functions:
    container_name: supabase-edge-functions
    image: supabase/edge-runtime:v1.67.3
    restart: unless-stopped
    volumes:
      - ./config/functions:/home/<USER>/functions
    depends_on:
      supabase-analytics:
        condition: service_healthy
    environment:
      JWT_SECRET: ${JWT_SECRET}
      SUPABASE_DB_URL: postgresql://postgres:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
    networks:
      - sems_network

  supabase-analytics:
    container_name: supabase-analytics
    image: supabase/logflare:1.5.2
    restart: unless-stopped
    ports:
      - "4000:4000"
    healthcheck:
      test: [ "CMD", "curl", "http://localhost:4000/health" ]
      interval: 10s
      timeout: 10s
      retries: 15
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      DB_HOSTNAME: supabase-db
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      LOGFLARE_API_KEY: ${LOGFLARE_API_KEY}
    networks:
      - sems_network

  supabase-db:
    container_name: supabase-db
    image: supabase/postgres:**********
    restart: unless-stopped
    volumes:
      - ./config/db/realtime.sql:/docker-entrypoint-initdb.d/migrations/99-realtime.sql
      - ./config/db/webhooks.sql:/docker-entrypoint-initdb.d/init-scripts/98-webhooks.sql
      - ./config/db/roles.sql:/docker-entrypoint-initdb.d/init-scripts/99-roles.sql
      - ./config/db/jwt.sql:/docker-entrypoint-initdb.d/init-scripts/99-jwt.sql
      - db_data:/var/lib/postgresql/data
      - ./config/db/_supabase.sql:/docker-entrypoint-initdb.d/migrations/97-_supabase.sql
      - ./config/db/logs.sql:/docker-entrypoint-initdb.d/migrations/99-logs.sql
      - ./config/db/pooler.sql:/docker-entrypoint-initdb.d/migrations/99-pooler.sql
      - db_config:/etc/postgresql-custom
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "postgres", "-h", "localhost" ]
      interval: 10s
      timeout: 10s
      retries: 15
    depends_on:
      supabase-vector:
        condition: service_healthy
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    networks:
      - sems_network

  supabase-vector:
    container_name: supabase-vector
    image: timberio/vector:0.40.0-alpine
    restart: unless-stopped
    volumes:
      - ./config/logs/vector.yml:/etc/vector/vector.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9001/health" ]
      interval: 10s
      timeout: 10s
      retries: 10
    environment:
      LOGFLARE_PUBLIC_ACCESS_TOKEN: ${LOGFLARE_API_KEY}
    networks:
      - sems_network

  supabase-pooler:
    container_name: supabase-pooler
    image: supabase/supavisor:2.5.7
    restart: unless-stopped
    ports:
      - "54322:5432"
      - "6543:6543"
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      DATABASE_URL: ecto://supabase_admin:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      SECRET_KEY_BASE: ${SUPAVISOR_SECRET_KEY_BASE}
      VAULT_ENC_KEY: ${SUPAVISOR_VAULT_ENC_KEY}
      API_JWT_SECRET: ${JWT_SECRET}
    networks:
      - sems_network

  supabase-mail:
    container_name: supabase-mail
    image: inbucket/inbucket:stable
    restart: unless-stopped
    ports:
      - '9000:9000'
      - '2500:2500'
    networks:
      - sems_network
