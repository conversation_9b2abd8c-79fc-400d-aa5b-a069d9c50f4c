import { Migration<PERSON>uilder } from 'node-pg-migrate';

export async function up(pgm: MigrationBuilder): Promise<void> {
    pgm.sql(`
        -- tenants tablosu
        CREATE TABLE tenants (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- users tablosu
        CREATE TABLE users (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- integrations tablosu
        CREATE TABLE integrations (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
            service_name VA<PERSON>HA<PERSON>(100) NOT NULL, -- 'notion', 'slack', 'google_calendar'
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(tenant_id, service_name)
        );

        -- oauth_tokens tablosu
        CREATE TABLE oauth_tokens (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
            integration_id UUID REFERENCES integrations(id) ON DELETE CASCADE,
            access_token TEXT NOT NULL,
            refresh_token TEXT,
            expires_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- agents tablosu
        CREATE TABLE agents (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            prompt TEXT,
            llm_provider VARCHAR(50) DEFAULT 'openai', -- 'openai', 'gemini'
            voice_enabled BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- agent_tasks tablosu
        CREATE TABLE agent_tasks (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
            flowise_flow_id VARCHAR(255),
            description TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- conversations tablosu
        CREATE TABLE conversations (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
            agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
            start_time TIMESTAMPTZ DEFAULT NOW()
        );

        -- agent_logs tablosu (konuşma geçmişi ve loglar için)
        CREATE TABLE agent_logs (
            id BIGSERIAL PRIMARY KEY,
            conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
            sender VARCHAR(50) NOT NULL, -- 'user' or 'agent'
            message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'audio'
            content TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
    `);
}

export async function down(pgm: MigrationBuilder): Promise<void> {
    pgm.sql(`
        DROP TABLE IF EXISTS agent_logs;
        DROP TABLE IF EXISTS conversations;
        DROP TABLE IF EXISTS agent_tasks;
        DROP TABLE IF EXISTS agents;
        DROP TABLE IF EXISTS oauth_tokens;
        DROP TABLE IF EXISTS integrations;
        DROP TABLE IF EXISTS users;
        DROP TABLE IF EXISTS tenants;
    `);
}
