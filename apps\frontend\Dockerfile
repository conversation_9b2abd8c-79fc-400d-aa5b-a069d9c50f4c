# Stage 1: Install Dependencies
FROM node:18-alpine AS deps
WORKDIR /app
RUN npm install -g pnpm

# Copy all workspace files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/frontend/package.json ./apps/frontend/package.json
COPY packages ./packages

# Install all dependencies
RUN pnpm install --frozen-lockfile

# Stage 2: Build the Frontend
FROM node:18-alpine AS builder
WORKDIR /app
RUN npm install -g pnpm

# Copy everything from deps stage
COPY --from=deps /app ./

# Copy source code
COPY apps/frontend/app ./apps/frontend/app
COPY apps/frontend/public ./apps/frontend/public
COPY apps/frontend/next.config.js ./apps/frontend/next.config.js
COPY apps/frontend/postcss.config.js ./apps/frontend/postcss.config.js
COPY apps/frontend/tailwind.config.ts ./apps/frontend/tailwind.config.ts
COPY apps/frontend/tsconfig.json ./apps/frontend/tsconfig.json

# Build the specific package
RUN pnpm --filter frontend build

# Stage 3: Production Image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy only necessary files from the builder stage
COPY --from=builder /app/apps/frontend/.next ./apps/frontend/.next
COPY --from=builder /app/apps/frontend/public ./apps/frontend/public
COPY --from=builder /app/apps/frontend/package.json ./apps/frontend/package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

# Set the command to run the frontend app
CMD ["pnpm", "--filter", "frontend", "start"]
