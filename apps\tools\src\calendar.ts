import { google, Auth } from 'googleapis';

// <PERSON>u fonksiyon, kullanıcıya ait bir OAuth token'ı ile Google API istemcisi oluşturur.
export function getGoogleAuthClient(accessToken: string, refreshToken?: string): Auth.OAuth2Client {
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  oauth2Client.setCredentials({
    access_token: accessToken,
    refresh_token: refreshToken,
  });

  return oauth2Client;
}

// Örnek bir fonksiyon: Google Calendar'daki etkinlikleri listeleme
export async function listCalendarEvents(auth: Auth.OAuth2Client) {
  const calendar = google.calendar({ version: 'v3', auth });

  try {
    const response = await calendar.events.list({
      calendarId: 'primary',
      timeMin: new Date().toISOString(),
      maxResults: 10,
      singleEvents: true,
      orderBy: 'startTime',
    });

    const events = response.data.items;
    if (events && events.length) {
      console.log('Upcoming 10 events:');
      events.map((event, i) => {
        const start = event.start?.dateTime || event.start?.date;
        console.log(`${start} - ${event.summary}`);
      });
      return events;
    } else {
      console.log('No upcoming events found.');
      return [];
    }
  } catch (error) {
    console.error("Error fetching from Google Calendar:", error);
    throw error;
  }
}

// TODO: Etkinlik oluşturma, güncelleme gibi diğer Calendar işlemleri için fonksiyonlar eklenebilir.
