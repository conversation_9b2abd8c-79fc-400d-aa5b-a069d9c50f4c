#!/bin/bash

# ==============================================================================
# SEMS Agents Projesi - Geliştirilmiş Başlatma Betiği
# ==============================================================================
# Bu betik, projenin tüm Docker servislerini tek bir komutla başlatır.
# .env.local dosyasının varlığına saygı duyar.
#
# KULLANIM: ./start.sh
# ==============================================================================

# Renkli çıktı için fonksiyonlar
print_info() {
    echo -e "\033[1;34m[BİLGİ]\033[0m $1"
}
print_success() {
    echo -e "\033[1;32m[BAŞARILI]\033[0m $1"
}
print_error() {
    echo -e "\033[1;31m[HATA]\033[0m $1"
}
print_warning() {
    echo -e "\033[1;33m[UYARI]\033[0m $1"
}


# Betiğin herhangi bir komutta hata alması durumunda durmasını sağla
set -e

# --- Adım 1: Proje Ortam (Environment) Dosyalarını Hazırla ---
print_info "--- Adım 1: Proje ortam yapılandırması hazırlanıyor... ---"

# Eğer .env.local varsa, kullanıcı kendi yapılandırmasını yönetiyor demektir.
if [ -f ".env.local" ]; then
    print_warning "Mevcut .env.local dosyası bulundu. Otomatik anahtar oluşturma atlanıyor."
    print_info "Yapılandırma bu dosyadan okunacak. Lütfen tüm gerekli değişkenlerin tanımlandığından emin olun."
    # .env dosyası yine de temel yapı için gerekebilir, yoksa oluşturalım.
    if [ ! -f ".env" ]; then
        print_info "Temel .env dosyası bulunamadı, .env.example dosyasından oluşturuluyor..."
        cp .env.example .env
    fi
else
    # .env.local yoksa, otomatik kurulum yap.
    print_info ".env.local dosyası bulunamadı. Otomatik yapılandırma yapılacak."

    # Ana .env dosyası yoksa, örnekten kopyala
    if [ ! -f ".env" ]; then
        print_info "Ana .env dosyası bulunamadı, .env.example dosyasından oluşturuluyor..."
        cp .env.example .env
    else
        print_info "Mevcut ana .env dosyası kullanılacak."
    fi

    # Fonksiyon: Değişkeni kontrol et, boşsa veya placeholder ise oluştur ve dosyaya yaz.
    ensure_secret() {
        local key=$1
        local file=$2
        # Değişkenin değerini al. Eğer satır yoksa veya değer boşsa, `current_val` boş olacaktır.
        current_val=$(grep -E "^${key}=" "$file" | cut -d'=' -f2-)
        if [ -z "$current_val" ] || [ "$current_val" == "YOUR_SECRET_HERE" ]; then
            local new_secret=$(openssl rand -hex 32)
            print_info "-> '$key' eksik veya geçersiz, yeni bir anahtar oluşturuluyor..."
            # Eğer anahtar dosyada varsa, satırı değiştir. Yoksa, sonuna ekle.
            if grep -q -E "^${key}=" "$file"; then
                sed -i.bak "s|^${key}=.*|${key}=${new_secret}|" "$file"
            else
                echo "${key}=${new_secret}" >> "$file"
            fi
        fi
    }

    # Gerekli tüm gizli anahtarları kontrol et/oluştur
    print_info ".env dosyası kontrol ediliyor ve eksik anahtarlar yapılandırılıyor..."
    ensure_secret "POSTGRES_PASSWORD" ".env"
    ensure_secret "JWT_SECRET" ".env"
    ensure_secret "ANON_KEY" ".env"
    ensure_secret "SERVICE_ROLE_KEY" ".env"
    ensure_secret "REALTIME_SECRET_KEY_BASE" ".env"
    ensure_secret "SUPAVISOR_SECRET_KEY_BASE" ".env"
    ensure_secret "SUPAVISOR_VAULT_ENC_KEY" ".env"
    ensure_secret "LOGFLARE_API_KEY" ".env"

    # sed tarafından oluşturulan yedek dosyaları temizle
    rm -f .env.bak
    print_success ".env dosyası başarıyla otomatik yapılandırıldı."
fi


# --- Adım 2: Gerekli Docker İmajlarını Çek ---
print_info "--- Adım 2: Gerekli Docker imajları çekiliyor... ---"
# pre-pull-images.sh betiği hala faydalı, çünkü imajları paralel indirme hatalarını önler.
./pre-pull-images.sh


# --- Adım 3: Tüm Servisleri Başlat ---
print_info "--- Adım 3: Docker Compose ile tüm servisler derleniyor ve başlatılıyor... ---"
docker compose up -d --build --wait

echo
print_success "--- KURULUM TAMAMLANDI! ---"
print_info "Geliştirme ortamınız başarıyla ayağa kaldırıldı."
echo
print_info "Frontend Uygulaması: http://localhost:3000"
print_info "Backend API:         http://localhost:8001"
print_info "Supabase Studio:     http://localhost:8082"
print_info "E-posta Sunucusu:    http://localhost:9000"
print_info "Flowise UI:          http://localhost:3001"
echo
