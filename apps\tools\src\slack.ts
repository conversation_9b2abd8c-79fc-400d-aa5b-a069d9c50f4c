import { WebClient } from '@slack/web-api';

// <PERSON><PERSON> fonksiyon, kullanıcıya ait bir OAuth token'ı ile Slack istemcisi oluşturur.
export function getSlackClient(accessToken: string): WebClient {
  return new WebClient(accessToken);
}

// Örnek bir fonksiyon: Slack'te bir kanala mesaj gönderme
export async function sendSlackMessage(accessToken: string, channel: string, text: string) {
  const slack = getSlackClient(accessToken);

  try {
    const response = await slack.chat.postMessage({
      channel: channel, // e.g., '#general' or a channel ID
      text: text,
    });
    console.log("Successfully sent message to Slack:", response.ts);
    return response;
  } catch (error) {
    console.error("Error sending message to Slack:", error);
    throw error;
  }
}

// TODO: Kanal listeleme, dosya yükleme gibi diğer Slack işlemleri için fonksiyonlar eklenebilir.
