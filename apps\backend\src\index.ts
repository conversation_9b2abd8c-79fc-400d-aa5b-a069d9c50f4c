import express, { Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const port = process.env.BACKEND_PORT || 8000;

app.use(cors());
app.use(express.json());

// Ana Rota
app.get('/', (req: Request, res: Response) => {
  res.send('SemsAgents Backend API is running!');
});

// TODO: Tenant, User, Agent, Auth rotaları buraya eklenecek.
// Örnek bir rota:
app.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'healthy', timestamp: new Date() });
});

app.listen(port, () => {
  console.log(`Backend server is listening on port ${port}`);
});
