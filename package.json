{"name": "sems-agents-monorepo", "private": true, "scripts": {"build": "pnpm --filter \"./apps/*\" build", "dev": "pnpm --filter frontend dev", "start": "pnpm --filter frontend start", "test": "vitest run", "db:migrate": "pnpm node-pg-migrate -j ts -m migrations", "db:create": "pnpm db:migrate create -- -j ts -m migrations --non-transactional"}, "devDependencies": {"@types/node": "^20.12.12", "typescript": "^5.4.5", "vitest": "^1.6.0", "node-pg-migrate": "^7.5.2", "pg": "^8.11.5"}}