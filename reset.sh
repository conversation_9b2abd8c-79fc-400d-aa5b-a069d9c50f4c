#!/bin/bash

# Renkli çıktı için fonksiyonlar
print_info() {
    echo -e "\033[1;34m[BİLGİ]\033[0m $1"
}
print_success() {
    echo -e "\033[1;32m[BAŞARILI]\033[0m $1"
}

# ==============================================================================
# SEMS Agents Projesi - Tam Ortam Sıfırlama Betiği
# ==============================================================================
# Bu betik, projenin TÜM Docker servislerini (Uygulamalar, Flowise, Supabase)
# durdurur, ilgili tüm verileri (volume'lar) ve yapılandırma dosyalarını (.env)
# kalıcı olarak siler.
# ==============================================================================

# --- Sıfırlama Fonksiyonları ---

reset_apps() {
    print_info "--- Ana uygulama servisleri (frontend, backend) durduruluyor ve verileri temizleniyor... ---"
    docker compose -f docker-compose.yml down -v --remove-orphans || true
    print_success "Ana uygulama servisleri ve ağları temizlendi."
}

reset_flowise() {
    print_info "--- Flowise servisi durduruluyor ve verileri temizleniyor... ---"
    docker compose -f docker/flowise/docker-compose.yml down -v --remove-orphans || true
    print_success "Flowise servisi ve Docker verileri temizlendi."
    print_info "Flowise .env dosyası siliniyor..."
    rm -f docker/flowise/.env
}

reset_supabase() {
    print_info "--- Supabase servisleri durduruluyor ve verileri temizleniyor... ---"
    print_info "Supabase container'ları ve Docker volume'ları siliniyor..."
    # .env dosyasının varlığını kontrol et, yoksa --env-file hataya neden olabilir.
    # Sıfırlama betiği olduğu için .env olmasa bile devam etmelidir.
    SUPABASE_ENV_FILE="docker/supabase/.env"
    COMPOSE_ARGS=()
    if [ -f "$SUPABASE_ENV_FILE" ]; then
        COMPOSE_ARGS+=(--env-file "$SUPABASE_ENV_FILE")
    fi

    docker compose \
      "${COMPOSE_ARGS[@]}" \
      -f docker/supabase/docker-compose.yml \
      -f docker/supabase/dev/docker-compose.dev.yml \
      down -v --remove-orphans || true

    print_success "Supabase servisleri ve Docker volume'leri temizlendi."

    print_info "Supabase yerel veri klasörleri (bind mounts) temizleniyor..."
    # Güvenlik için klasörün varlığını kontrol et
    if [ -d "docker/supabase/volumes" ]; then
        rm -rf docker/supabase/volumes/db/data docker/supabase/volumes/storage
        print_success "Supabase yerel veri klasörleri temizlendi."
    fi

    print_info "Supabase .env dosyası siliniyor..."
    rm -f docker/supabase/.env
}

reset_main_env() {
    print_info "--- Ana .env dosyası temizleniyor... ---"
    rm -f .env
    print_success "Ana .env dosyası silindi."
}

prune_docker() {
    print_info "--- Kullanılmayan TÜM Docker kaynakları (imajlar, volume'ler, ağlar) temizleniyor... ---"
    # Bu komut, durdurulmuş tüm container'ları, kullanılmayan tüm ağları,
    # tüm sahipsiz (dangling) imajları ve tüm sahipsiz veri alanlarını (volume) siler.
    # -a bayrağı, sahipsiz olanlara ek olarak kullanılmayan tüm imajları da temizler.
    docker system prune -a --volumes -f

    print_info "--- Docker build cache temizleniyor... ---"
    docker builder prune -f

    print_success "Docker sistem temizliği tamamlandı."
}
# --- Ana Betik Mantığı ---

usage() {
  echo "Kullanım: $0 [all|supabase|flowise|apps] [-y]"
  echo
  echo "Hedefler:"
  echo "  all:      Projedeki TÜM servisleri, verileri ve .env dosyalarını sıfırlar."
  echo "  supabase: Sadece Supabase servislerini ve verilerini sıfırlar."
  echo "  flowise:  Sadece Flowise servisini ve verilerini sıfırlar."
  echo "  apps:     Sadece özel uygulama servislerini (frontend, backend vb.) ve ana .env dosyasını sıfırlar."
  echo "  prune:    'all' hedefini çalıştırdıktan sonra sistemdeki kullanılmayan TÜM Docker imajlarını, volume'lerini ve ağlarını temizler (Derin Temizlik)."
  echo
  echo "Seçenekler:"
  echo "  -y:       Onay istemeden işlemi direkt gerçekleştirir (otomasyon için)."
  exit 1
}

if [ "$#" -eq 0 ]; then
    usage
fi

# Argümanları işle
TARGET=""
FORCE="false"
for arg in "$@"; do
  case $arg in
    all|supabase|flowise|apps|prune)
      TARGET=$arg
      ;;
    -y)
      FORCE="true"
      ;;
  esac
done

if [ -z "$TARGET" ]; then
    echo "Hata: Geçerli bir hedef belirtilmedi (all, supabase, flowise, apps, prune)."
    usage
fi

# Betiğin herhangi bir komutta hata alması durumunda durmasını sağla
set -e

# Kullanıcıdan onay al
if [[ "$FORCE" == "false" ]]; then
    echo "UYARI: '$TARGET' hedefi için sıfırlama işlemi başlatılacak. Bu işlem geri alınamaz!"
    read -p "Devam etmek istediğinizden emin misiniz? (y/N) " -n 1 -r
    echo # Yeni satıra geç
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "İşlem iptal edildi."
        exit 1
    fi
fi

case $TARGET in
    all)
        reset_apps; reset_flowise; reset_supabase; reset_main_env; ;;
    supabase)
        reset_supabase; ;;
    flowise)
        reset_flowise; ;;
    apps)
        reset_apps; reset_main_env; ;;
    prune)
        reset_apps; reset_flowise; reset_supabase; reset_main_env; prune_docker; ;;
esac

echo
print_success "--- '$TARGET' HEDEFİ İÇİN SIFIRLAMA TAMAMLANDI! ---"
print_info "Sistemi yeniden kurmak için './start-dev.sh' komutunu çalıştırabilirsiniz."
echo