{"name": "backend", "version": "1.0.0", "description": "Backend API for SemsAgents", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.12.12", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}