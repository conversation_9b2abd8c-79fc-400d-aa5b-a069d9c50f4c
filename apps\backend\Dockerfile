# Stage 1: Install Dependencies
FROM node:18-alpine AS deps
WORKDIR /app
RUN npm install -g pnpm

# Copy dependency definition files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Install all dependencies
RUN pnpm install --frozen-lockfile

# Stage 2: Build the Backend
FROM node:18-alpine AS builder
WORKDIR /app

# Copy all source code and installed dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the specific package
RUN pnpm --filter backend build

# Stage 3: Production Image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy only necessary files from the builder stage
COPY --from=builder /app/apps/backend/dist ./apps/backend/dist
COPY --from=builder /app/apps/backend/package.json ./apps/backend/package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

# Set the command to run the backend app
CMD ["pnpm", "--filter", "backend", "start"]
