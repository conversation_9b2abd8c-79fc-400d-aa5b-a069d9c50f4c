# Supabase
# Public URL for the frontend (browser access). This is used by the client-side Supabase library.
NEXT_PUBLIC_SUPABASE_URL="http://localhost:8000"
NEXT_PUBLIC_SUPABASE_ANON_KEY="YOUR_SUPABASE_ANON_KEY" # Copy from docker/supabase/.env

# Internal URL for the backend (server-to-server communication within Docker).
# This uses the Docker service name 'kong' instead of 'localhost'.
SUPABASE_URL="http://kong:8000"
SUPABASE_SERVICE_ROLE_KEY="YOUR_SUPABASE_SERVICE_ROLE_KEY" # Copy from docker/supabase/.env

# LLM Providers
OPENAI_API_KEY="********************************************************************************************************************************************************************"
GEMINI_API_KEY="AIzaSyCzPevUGsPc2K8QKK-6oZdsodIWi8ZG6l4"

# Flowise
FLOWISE_API_ENDPOINT="http://localhost:3001" # Örnek, Flowise container adresine göre değişebilir

# OAuth - Her servis için ayrı client id ve secret
# Google
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/auth/google/callback"

# Notion
NOTION_CLIENT_ID="YOUR_NOTION_CLIENT_ID"
NOTION_CLIENT_SECRET="YOUR_NOTION_CLIENT_SECRET"
NOTION_REDIRECT_URI="http://localhost:8000/api/auth/notion/callback"

# Slack
SLACK_CLIENT_ID="YOUR_SLACK_CLIENT_ID"
SLACK_CLIENT_SECRET="YOUR_SLACK_CLIENT_SECRET"
SLACK_REDIRECT_URI="http://localhost:8000/api/auth/slack/callback"

# JWT Secret for session management
JWT_SECRET="a_very_strong_and_secret_key_for_jwt"

# Backend Server Port
BACKEND_PORT=8000

# Supabase Auth & Studio Default Settings (to prevent warnings)
SITE_URL="http://localhost:3000"
DISABLE_SIGNUP=false
DEFAULT_ORGANIZATION_NAME="SEMS Agents"
DEFAULT_PROJECT_NAME="Local Development"
ADDITIONAL_REDIRECT_URLS=""
