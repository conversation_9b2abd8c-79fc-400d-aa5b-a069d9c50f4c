import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css"; // Tailwind stillerini import et

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "SemsAgents - Your AI Agent Workforce",
  description: "Build and manage your custom team of AI agents.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr">
      <body className={inter.className}>{children}</body>
    </html>
  );
}
