import { OpenAI } from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';

dotenv.config();

// --- LLM İstemcilerini Başlatma ---
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

// --- <PERSON><PERSON><PERSON><PERSON><PERSON> (Interfaces) ---
interface AgentInput {
  prompt: string;
  conversationHistory: any[]; // Detaylı bir arayüzle değiştirilebilir
  llmProvider: 'openai' | 'gemini';
  flowiseFlowId?: string;
  tools?: any[]; // Tool'lar için detaylı arayüz
}

interface AgentOutput {
  response: string;
  debugInfo?: any;
}

// --- Ana Agent Çalıştırma Fonksiyonu ---
export async function runAgent(input: AgentInput): Promise<AgentOutput> {
  console.log(`Running agent with ${input.llmProvider}...`);

  // 1. Flowise akışını tetikle (eğer varsa)
  if (input.flowiseFlowId) {
    console.log(`Handling Flowise flow: ${input.flowiseFlowId}`);
    // TODO: Flowise API'sine istek at ve akışı yönet
  }

  // 2. LLM'i seç ve çalıştır
  let llmResponse: string;

  if (input.llmProvider === 'gemini') {
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    const result = await model.generateContent(input.prompt);
    const response = await result.response;
    llmResponse = response.text();
  } else { // Varsayılan olarak OpenAI
    const completion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: input.prompt }],
      model: 'gpt-4-turbo', // veya başka bir model
    });
    llmResponse = completion.choices[0]?.message?.content ?? "No response from OpenAI.";
  }

  // 3. Araçları kullan (eğer gerekliyse)
  // TODO: LLM yanıtını analiz et ve gerekli araçları tetikle (Notion, Slack vb.)

  return {
    response: llmResponse,
  };
}

// --- Örnek Kullanım ---
async function main() {
  const userInput: AgentInput = {
    prompt: "Merhaba, bugün bana nasıl yardımcı olabilirsin?",
    conversationHistory: [],
    llmProvider: 'openai',
  };

  const result = await runAgent(userInput);
  console.log("Agent Response:", result.response);
}

// Bu dosya doğrudan çalıştırıldığında main fonksiyonunu çağır
if (require.main === module) {
  main();
}
