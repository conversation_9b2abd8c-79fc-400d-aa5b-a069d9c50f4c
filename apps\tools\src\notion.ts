import { Client } from '@notionhq/client';

// <PERSON><PERSON> fonksiyon, kullanıcıya ait bir OAuth token'ı ile Notion istemcisi oluşturur.
export function getNotionClient(accessToken: string): Client {
  return new Client({
    auth: accessToken,
  });
}

// Örnek bir fonksiyon: Notion'da bir veritabanındaki sayfaları listeleme
export async function listNotionDatabasePages(accessToken: string, databaseId: string) {
  const notion = getNotionClient(accessToken);

  try {
    const response = await notion.databases.query({
      database_id: databaseId,
    });
    console.log("Successfully fetched pages from Notion:", response.results);
    return response.results;
  } catch (error) {
    console.error("Error fetching from Notion:", error);
    throw error;
  }
}

// TODO: Sayfa oluşturma, güncelleme gibi diğer Notion işlemleri için fonksiyonlar eklenebilir.
