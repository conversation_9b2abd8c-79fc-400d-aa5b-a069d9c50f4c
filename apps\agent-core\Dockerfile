# Stage 1: Install Dependencies
FROM node:18-alpine AS deps
WORKDIR /app
RUN npm install -g pnpm

# Copy all workspace files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/agent-core/package.json ./apps/agent-core/package.json
COPY packages ./packages

# Install all dependencies
RUN pnpm install --frozen-lockfile

# Stage 2: Build the Agent Core
FROM node:18-alpine AS builder
WORKDIR /app
RUN npm install -g pnpm

# Copy everything from deps stage
COPY --from=deps /app ./

# Copy source code
COPY apps/agent-core/src ./apps/agent-core/src
COPY apps/agent-core/tsconfig.json ./apps/agent-core/tsconfig.json

# Build the specific package
RUN pnpm --filter agent-core build

# Stage 3: Production Image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy only necessary files from the builder stage
COPY --from=builder /app/apps/agent-core/dist ./apps/agent-core/dist
COPY --from=builder /app/apps/agent-core/package.json ./apps/agent-core/package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

# Set the command to run the agent-core app
CMD ["pnpm", "--filter", "agent-core", "start"]
